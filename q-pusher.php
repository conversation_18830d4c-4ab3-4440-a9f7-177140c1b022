<?php

/**
 * Plugin Name: Q-Pusher
 * Description: <PERSON>-<PERSON><PERSON><PERSON> brings advanced push notifications and Progressive Web App (PWA) capabilities to your WordPress site. Seamlessly integrated with Formidable Forms, Q-Pusher enables you to send real-time push notifications to users on form submissions and other events.
 * Version: 2.2.2
 * Author: Q-Ai
 * Text Domain: q-pusher-q-pwa
 */

if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    add_action('admin_notices', function () {
        echo '<div class="error"><p>Q Pusher requires PHP 8.1 or higher. Your PHP version is ' . PHP_VERSION . '</p></div>';
    });
    return;
}

defined('ABSPATH') or die('No script kiddies please!');

// Temporarily suppress specific deprecation notices
error_reporting(E_ALL & ~E_DEPRECATED);

// Define constants for plugin directory and URL
define('Q_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('Q_PLUGIN_URL', plugin_dir_url(__FILE__));
define('Q_DB_VERSION', '1.1');

// Include required files
require_once Q_PLUGIN_DIR . 'includes/class-q-diagnostics.php';
require_once Q_PLUGIN_DIR . 'includes/notification-analytics.php';
require_once Q_PLUGIN_DIR . 'includes/firebase-functions.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-firebase-manager.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-activator.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-pwa-settings.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-pwa-manifest.php';
require_once Q_PLUGIN_DIR . 'includes/class-q-pwa-analytics.php';

// Initialize settings
add_action('init', ['Q_PWA_Settings', 'init']);
add_action('init', ['Q_PWA_Manifest', 'init']);



// Handle manifest.json requests early
add_action('init', 'q_handle_manifest_request', 1);

/**
 * Handle manifest.json requests directly
 */
function q_handle_manifest_request()
{
    $request_uri = $_SERVER['REQUEST_URI'] ?? '';

    // Check if this is a manifest.json request
    if (
        strpos($request_uri, '/manifest.json') !== false ||
        (isset($_GET['q_manifest']) && $_GET['q_manifest'] == '1')
    ) {

        // Debug logging
        // error_log('Q-PWA: Manifest request detected: ' . $request_uri);

        // Set headers
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, no-store, must-revalidate'); // Prevent caching
        header('Pragma: no-cache');
        header('Expires: 0');
        header('Access-Control-Allow-Origin: *');

        // Generate manifest using the PWA Manifest class
        if (class_exists('Q_PWA_Manifest')) {
            $manifest = Q_PWA_Manifest::generate_manifest();
            // error_log('Q-PWA: Generated manifest via class');
        } else {
            // Fallback basic manifest
            $manifest = [
                'name' => get_bloginfo('name'),
                'short_name' => get_bloginfo('name'),
                'description' => get_bloginfo('description'),
                'start_url' => '/',
                'display' => 'standalone',
                'theme_color' => '#000000',
                'background_color' => '#ffffff',
                'icons' => []
            ];
            // error_log('Q-PWA: Generated fallback manifest');
        }

        echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        exit;
    }
}

// Change the conditional include to use proper hook
/**
 * Load Formidable Forms dependencies if the class exists.
 */
add_action('plugins_loaded', 'q_load_formidable_dependencies', 20);

/**
 * Load Formidable Forms dependencies if the class exists.
 */
function q_load_formidable_dependencies()
{
    if (class_exists('FrmFormAction')) {
        // Include Formidable actions if the class is available
        require_once Q_PLUGIN_DIR . 'includes/formidable-actions.php';
    } else {
        // Show admin notice if Formidable Forms is not active
        add_action('admin_notices', 'q_formidable_missing_notice');
    }
}

/**
 * Activation hook to set up the plugin.
 */
register_activation_hook(__FILE__, function () {
    Q_Activator::activate();
    q_activate();

    // Flush rewrite rules to ensure manifest.json works
    flush_rewrite_rules();
});

/**
 * Create the Q-Notify form and copy the service worker on activation.
 */
function q_activate()
{
    q_create_notify_form();
    q_copy_service_worker();
}

/**
 * Create the Q-Notify form if it doesn't exist.
 */
function q_create_notify_form()
{
    if (class_exists('FrmForm')) {
        global $wpdb;
        $form_name = 'Q-Notify';
        $form_exists = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$wpdb->prefix}frm_forms WHERE name = %s", $form_name));

        if (!$form_exists) {
            // Create form with specified values
            $form_values = array(
                'name' => $form_name,
                'description' => 'Form for sending notifications',
                'status' => 'published',
                'is_template' => 0,
                'form_key' => uniqid('qnotify_') // Add unique key
            );
            $form_id = FrmForm::create($form_values);

            if ($form_id) {
                q_add_fields_to_form($form_id);
            }
        }
    }
}

/**
 * Add fields to the Q-Notify form.
 */
function q_add_fields_to_form($form_id)
{
    $fields = array(
        array(
            'name' => 'Notification Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter the type of notification (e.g., Alert, Update, News, etc.)'
        ),
        array(
            'name' => 'Audience Type',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Specify the type of audience (e.g., All Users, Subscribers, Admins, etc.)'
        ),
        array(
            'name' => 'Audience',
            'type' => 'text',
            'form_id' => $form_id,
            'description' => 'Enter audience emails'
        )
    );

    // Add each field to the form
    foreach ($fields as $field) {
        FrmField::create($field);
    }
}

/**
 * Copy the service worker to the root directory.
 */
function q_copy_service_worker()
{
    $sw_source = Q_PLUGIN_DIR . 'firebase-messaging-sw.js';
    $sw_dest = ABSPATH . 'firebase-messaging-sw.js';

    // Debug logging
    // error_log('Q-Pusher: Attempting to copy service worker');
    // error_log('Q-Pusher: Source path: ' . $sw_source);
    // error_log('Q-Pusher: Destination path: ' . $sw_dest);

    // Check if the source file exists
    if (!file_exists($sw_source)) {
        // error_log('Q-Pusher: Service worker source file not found at ' . $sw_source);
        return false;
    }

    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id'),
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id')
        );
    }

    // Verify Firebase configuration
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            // error_log("Q-Pusher: Missing Firebase configuration: {$field}");
            return false;
        }
    }

    try {
        // Read the service worker template
        $sw_content = file_get_contents($sw_source);
        if ($sw_content === false) {
            throw new Exception('Failed to read service worker source file');
        }

        // Prepend a dynamic version comment
        $plugin_version = defined('Q_PUSHER_PLUGIN_VERSION') ? Q_PUSHER_PLUGIN_VERSION : 'unknown';
        $version_comment = "// Version: {$plugin_version} - " . date('Y-m-d H:i:s') . "\n";
        $sw_content = $version_comment . $sw_content;

        $sw_content = str_replace(
            [
                'FIREBASE_API_KEY',
                'FIREBASE_AUTH_DOMAIN',
                'FIREBASE_PROJECT_ID',
                'FIREBASE_STORAGE_BUCKET',
                'FIREBASE_MESSAGING_SENDER_ID',
                'FIREBASE_APP_ID'
            ],
            [
                $firebase_config['apiKey'],
                $firebase_config['authDomain'],
                $firebase_config['projectId'],
                $firebase_config['storageBucket'],
                $firebase_config['messagingSenderId'],
                $firebase_config['appId']
            ],
            $sw_content
        );

        // --- Inject offline and caching strategy settings ---
        $caching_strategy = get_option('q_pwa_caching_strategy', 'network-first');
        $offline_page_id = get_option('q_pwa_offline_page_id', 0);
        $offline_url = '';
        if ($offline_page_id) {
            $offline_url = get_permalink($offline_page_id);
        }
        $sw_content = preg_replace(
            "/const\\s+Q_PWA_CACHING_STRATEGY\\s*=\\s*['\\\"](.*?)['\\\"];/",
            "const Q_PWA_CACHING_STRATEGY = '" . addslashes($caching_strategy) . "';",
            $sw_content
        );
        $sw_content = preg_replace(
            "/const\\s+Q_PWA_OFFLINE_URL\\s*=\\s*['\\\"](.*?)['\\\"];/",
            "const Q_PWA_OFFLINE_URL = '" . addslashes($offline_url) . "';",
            $sw_content
        );

        // If offline page is set, precache it (add to install event)
        // (If not already present, add a basic install event for precaching)
        if ($offline_url) {
            if (strpos($sw_content, 'self.addEventListener("install"') === false && strpos($sw_content, "self.addEventListener('install'", ) === false) {
                $install_code = "\nself.addEventListener('install', function(event) {\n  event.waitUntil((async () => {\n    const cache = await caches.open(Q_PWA_CACHE_NAME);\n    await cache.add('{$offline_url}');\n  })());\n});\n";
                $sw_content = $install_code . $sw_content;
            } else {
                // If install event exists, you may need to merge logic (not implemented here)
            }
        }

        // Ensure WordPress root directory is writable
        if (!is_writable(ABSPATH)) {
            throw new Exception('WordPress root directory is not writable');
        }

        // Delete existing service worker if it exists
        if (file_exists($sw_dest) && !unlink($sw_dest)) {
            throw new Exception('Failed to delete existing service worker file');
        }

        // Write the configured service worker
        if (file_put_contents($sw_dest, $sw_content) === false) {
            throw new Exception('Failed to write service worker file');
        }

        // Set proper permissions
        if (!chmod($sw_dest, 0644)) {
            throw new Exception('Failed to set service worker file permissions');
        }

        // Add rewrite rules
        add_rewrite_rule(
            '^firebase-messaging-sw\.js$',
            'index.php?q_service_worker=1',
            'top'
        );

        // Add manifest.json rewrite rule
        add_rewrite_rule(
            '^manifest\.json$',
            'index.php?q_manifest=1',
            'top'
        );

        // Flush rewrite rules
        flush_rewrite_rules();

        return true;

    } catch (Exception $e) {
        // error_log('Q-Pusher Error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Deactivation hook to clean up on plugin deactivation.
 */
register_deactivation_hook(__FILE__, 'q_deactivate');

/**
 * Remove the service worker file on deactivation.
 */
function q_deactivate()
{
    // Remove service worker file
    $sw_file = ABSPATH . 'firebase-messaging-sw.js';
    if (file_exists($sw_file)) {
        unlink($sw_file);
    }
}

/**
 * Enqueue scripts and styles for the frontend.
 */
add_action('wp_enqueue_scripts', 'q_enqueue_scripts');

/**
 * Enqueue scripts and styles for the frontend.
 */
function q_enqueue_scripts()
{
    q_enqueue_firebase_scripts();
    q_enqueue_custom_scripts();
}

/**
 * Enqueue Firebase SDK scripts.
 */
function q_enqueue_firebase_scripts()
{
    // Firebase SDK is now imported as a module in firebase-init.js
}

/**
 * Enqueue custom scripts for the plugin.
 */
function q_enqueue_custom_scripts()
{
    // Try to get Firebase configuration from JSON first
    $firebase_config = array();
    $json_config = get_option('q_firebase_config');

    if (!empty($json_config)) {
        $decoded = json_decode($json_config, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $firebase_config = $decoded;
        }
    }

    // If JSON config is not available or invalid, try individual settings
    if (empty($firebase_config) || !isset($firebase_config['projectId'])) {
        $firebase_config = array(
            'apiKey' => get_option('q_firebase_api_key'),
            'authDomain' => get_option('q_firebase_auth_domain'),
            'projectId' => get_option('q_firebase_project_id'),
            'storageBucket' => get_option('q_firebase_storage_bucket'),
            'messagingSenderId' => get_option('q_firebase_messaging_sender_id'),
            'appId' => get_option('q_firebase_app_id'),
            'measurementId' => get_option('q_firebase_measurement_id'),
            'publicVapidKey' => get_option('q_firebase_public_vapid_key'),
        );
    }

    // Verify required fields
    $required_fields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
    $missing_fields = array();

    foreach ($required_fields as $field) {
        if (empty($firebase_config[$field])) {
            $missing_fields[] = $field;
        }
    }

    if (!empty($missing_fields)) {
        // error_log('Q-Pusher: Missing required Firebase configuration fields: ' . implode(', ', $missing_fields));
    }

    // Add config as a global variable
    wp_register_script('q-firebase-config', '', array(), '', true);
    wp_enqueue_script('q-firebase-config');
    wp_add_inline_script(
        'q-firebase-config',
        'window.q_firebase_config = ' . json_encode($firebase_config) . ';',
        'before'
    );

    // Suppress jQuery migrate warnings
    wp_add_inline_script('jquery-migrate', 'jQuery.migrateMute = true;', 'before');

    // Add type="module" to script tags
    add_filter('script_loader_tag', function ($tag, $handle, $src) {
        if (in_array($handle, ['q-firebase-init', 'q-subscription', 'q-unsubscribe', 'q-notification-badge'])) {
            $tag = '<script type="module" src="' . esc_url($src) . '"></script>';
        }
        return $tag;
    }, 10, 3);
    


    // Enqueue the module scripts
    wp_enqueue_script(
        'q-firebase-init',
        Q_PLUGIN_URL . 'includes/js/firebase-init.js',
        array('q-firebase-config'),
        '1.0.0',
        true
    );

    wp_enqueue_script(
        'q-subscription',
        Q_PLUGIN_URL . 'includes/js/subscription.js',
        array('q-firebase-init', 'jquery'),
        '1.0.0',
        true
    );

    // Pass AJAX URL and nonce to the subscription script
    wp_localize_script('q-subscription', 'q_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('q_subscribe_nonce')
    ));

    // Enqueue the PWA badge script
    wp_enqueue_script(
        'q-pwa-badge',
        Q_PLUGIN_URL . 'includes/js/pwa-badge.js',
        array('q-firebase-init'),
        '1.0.0',
        true
    );
    
   

    // Enqueue the unsubscribe module script
    wp_enqueue_script(
        'q-unsubscribe',
        Q_PLUGIN_URL . 'includes/js/unsubscribe.js',
        array('q-firebase-init', 'jquery'),
        '1.0.0',
        true
    );

    // Pass AJAX URL and nonce to the unsubscribe script
    wp_localize_script('q-unsubscribe', 'q_ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('q_subscribe_nonce')
    ));
    
    // Pass notification settings to JavaScript
    wp_localize_script('q-notification-renderer', 'q_notification_settings', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('q_notification_nonce'),
        'site_url' => site_url(),
        'plugin_url' => Q_PLUGIN_URL
    ));
}



/**
 * Add headers for the service worker to allow it to be served correctly.
 */
add_action('init', 'q_add_service_worker_headers');

/**
 * Add headers for service worker and handle requests
 */
function q_add_service_worker_headers()
{
    if (isset($_GET['q_service_worker'])) {
        header('Content-Type: application/javascript');
        header('Service-Worker-Allowed: /');
        header('Cache-Control: no-cache');

        $sw_path = ABSPATH . 'firebase-messaging-sw.js';
        if (file_exists($sw_path)) {
            readfile($sw_path);
        } else {
            // error_log('Q-Pusher: Service worker file not found at: ' . $sw_path);
            status_header(404);
        }
        exit;
    }
}
add_action('init', 'q_add_service_worker_headers');

/**
 * Add query vars for service worker
 */
function q_add_query_vars($vars)
{
    $vars[] = 'q_service_worker';
    $vars[] = 'q_manifest';
    return $vars;
}
add_filter('query_vars', 'q_add_query_vars');

/**
 * Shortcode to display a subscription button for notifications.
 */
add_shortcode('q_subscribe_button', 'q_subscribe_button_shortcode');

/**
 * Register shortcode to display an unsubscribe button for push notifications.
 */
add_shortcode('q_unsubscribe_button', 'q_unsubscribe_button_shortcode');

/**
 * Shortcode to display a subscription button for notifications.
 */
function q_subscribe_button_shortcode($atts = [])
{
    // Parse shortcode attributes with defaults
    $atts = shortcode_atts([
        'text' => 'Enable Notifications', // Default button text
        'class' => 'ui-button primary',         // Default CSS classes
        'icon' => 'bi bi-bell'             // Default icon class
    ], $atts, 'q_subscribe_button');

    // Sanitize the attributes
    $button_text = sanitize_text_field($atts['text']);
    $button_class = sanitize_text_field($atts['class']);
    $icon_class = sanitize_text_field($atts['icon']);




    ob_start(); ?>
        <?php
        // Begin user-scoped filtering
        $user_id = get_current_user_id();
        if ( ! $user_id ) {
            return ''; // Return empty string if user is not logged in
        }
    ?>
    <button id="q-subscribe-button" class="<?php echo esc_attr($button_class . ' q-hidden'); ?>">
        <i style="padding-right:5px" class="<?php echo esc_attr($icon_class); ?>"></i> <?php echo esc_html($button_text); ?>
    </button>
    <?php
    return ob_get_clean();
}

/**
 * Callback for the [q_unsubscribe_button] shortcode.
 *
 * @param array $atts Shortcode attributes.
 * @return string Rendered HTML for the unsubscribe button.
 */
function q_unsubscribe_button_shortcode($atts = [])
{
    // Parse shortcode attributes with defaults
    $atts = shortcode_atts([
        'text' => 'Disable Notifications', // Default button text
        'class' => 'ui-button',  // Default CSS classes
        'icon' => 'bi bi-bell-slash'  // Default icon class
    ], $atts, 'q_unsubscribe_button');

    // Sanitize the attributes
    $button_text = sanitize_text_field($atts['text']);
    $button_class = sanitize_text_field($atts['class']);
    $icon_class = sanitize_text_field($atts['icon']);

    ob_start(); ?>
    <?php
        // Begin user-scoped filtering
        $user_id = get_current_user_id();
        if ( ! $user_id ) {
            return ''; // Return empty string if user is not logged in
        }
    ?>
    <button id="q-unsubscribe-button" class="<?php echo esc_attr($button_class); ?>">
        <i style="padding-right:5px" class="<?php echo esc_attr($icon_class); ?>"></i> <?php echo esc_html($button_text); ?>
    </button>
    <?php
    return ob_get_clean();
}

/**
 * Handle AJAX request to save the subscription token.
 */
add_action('wp_ajax_q_subscribe', 'q_subscribe_callback');
add_action('wp_ajax_nopriv_q_subscribe', 'q_subscribe_callback'); // For non-logged-in users

/**
 * Handle AJAX request to save the subscription token.
 */
function q_subscribe_callback()
{
    try {
        // Verify nonce (accept either 'nonce' or 'security' parameter to improve compatibility)
        $passed_nonce = isset($_POST['nonce']) ? sanitize_text_field($_POST['nonce']) : ( isset($_POST['security']) ? sanitize_text_field($_POST['security']) : '' );

        if ( empty( $passed_nonce ) ) {
            wp_send_json_error( [ 'message' => 'Security token is required' ] );
            return;
        }

        // Verify nonce against correct action
        if ( ! wp_verify_nonce( $passed_nonce, 'q_subscribe_nonce' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid security token' ] );
            return;
        }

        // Validate token presence
        if (empty($_POST['token'])) {
            wp_send_json_error([
                'message' => 'Token is required'
            ]);
            return;
        }

        $token = sanitize_text_field($_POST['token']);

        // Validate token format
        if (strlen($token) < 50) {
            wp_send_json_error([
                'message' => 'Invalid token format'
            ]);
            return;
        }

        $user_id = get_current_user_id();

        if (!$user_id) {
            wp_send_json_error([
                'message' => 'User must be logged in'
            ]);
            return;
        }

        // Store the token
        $result = update_user_meta($user_id, 'q_push_token', $token);

        if ($result === false) {
            wp_send_json_error([
                'message' => 'Failed to save subscription'
            ]);
            return;
        }

        wp_send_json_success([
            'message' => 'Subscription successful!',
            'userId' => $user_id
        ]);

    } catch (Exception $e) {
        // error_log('Subscription error: ' . $e->getMessage());
        wp_send_json_error([
            'message' => 'An unexpected error occurred'
        ]);
    }
}

/**
 * Handle AJAX request to remove the subscription token from the current user.
 */
function q_unsubscribe_callback()
{
    try {
        // Verify nonce (accept both 'nonce' or 'security' for compatibility)
        $passed_nonce = isset($_POST['nonce']) ? sanitize_text_field($_POST['nonce']) : ( isset($_POST['security']) ? sanitize_text_field($_POST['security']) : '' );

        if ( empty( $passed_nonce ) ) {
            wp_send_json_error( [ 'message' => 'Security token is required' ] );
            return;
        }

        // Re-use the same nonce action as subscription for simplicity
        if ( ! wp_verify_nonce( $passed_nonce, 'q_subscribe_nonce' ) ) {
            wp_send_json_error( [ 'message' => 'Invalid security token' ] );
            return;
        }

        $user_id = get_current_user_id();

        if ( ! $user_id ) {
            wp_send_json_error( [ 'message' => 'User must be logged in' ] );
            return;
        }

        // Remove the stored push token
        delete_user_meta( $user_id, 'q_push_token' );

        // Attempt to invalidate token on Firebase if integration is available
        if ( class_exists( 'Q_Firebase_Manager' ) ) {
            try {
                Q_Firebase_Manager::invalidate_token( $user_id );
            } catch ( Exception $e ) {
                // error_log( 'Failed to invalidate Firebase token: ' . $e->getMessage() );
            }
        }

        wp_send_json_success( [ 'message' => 'Unsubscribed successfully!' ] );

    } catch ( Exception $e ) {
        // error_log( 'Unsubscribe error: ' . $e->getMessage() );
        wp_send_json_error( [ 'message' => 'An unexpected error occurred' ] );
    }
}

// Register AJAX handlers for unsubscribe requests
add_action('wp_ajax_q_unsubscribe', 'q_unsubscribe_callback');
add_action('wp_ajax_nopriv_q_unsubscribe', 'q_unsubscribe_callback');

/**
 * Display an admin notice if Formidable Forms is missing.
 */
function q_formidable_missing_notice()
{
    ?>
    <div class="notice notice-error is-dismissible">
        <p><?php _e('Q Pusher plugin requires Formidable Forms to be installed and activated.', 'q-pusher-q-pwa'); ?>
        </p>
    </div>
    <?php
}

// Add AJAX handler for removing subscribers
add_action('wp_ajax_q_remove_subscriber', 'q_remove_subscriber_callback');

function q_remove_subscriber_callback()
{
    check_ajax_referer('remove_subscriber_' . $_POST['user_id'], 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'q-pusher-q-pwa'));
        return;
    }

    $user_id = intval($_POST['user_id']);

    // Delete the push token
    $result = delete_user_meta($user_id, 'q_push_token');

    if ($result) {
        // Invalidate the token on Firebase if possible
        try {
            if (class_exists('Q_Firebase_Manager')) {
                Q_Firebase_Manager::invalidate_token($user_id);
            }
        } catch (Exception $e) {
            // error_log('Failed to invalidate Firebase token: ' . $e->getMessage());
        }

        wp_send_json_success(__('Subscriber removed successfully.', 'q-pusher-q-pwa'));
    } else {
        wp_send_json_error(__('Failed to remove subscriber.', 'q-pusher-q-pwa'));
    }
}

// Add AJAX handler for sending test notifications
add_action('wp_ajax_q_send_test_notification', 'q_send_test_notification_callback');

function q_send_test_notification_callback()
{
    // Verify nonce
    $user_id = intval($_POST['user_id']);
    check_ajax_referer('test_notification_' . $user_id, 'nonce');

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'q-pusher-q-pwa'));
        return;
    }

    // Get the user's push token
    $token = get_user_meta($user_id, 'q_push_token', true);

    if (empty($token)) {
        wp_send_json_error(__('No push token found for this user.', 'q-pusher-q-pwa'));
        return;
    }
    
    // Validate token format
    if (!preg_match('/^[a-zA-Z0-9:_-]{20,300}$/', $token)) {
        wp_send_json_error(__('Invalid token format. Please re-subscribe to notifications.', 'q-pusher-q-pwa'));
        return;
    }

    // Send test notification
    $title = __('Test Notification', 'q-pusher-q-pwa');
    $message = __('This is a test notification from Q-Pusher.', 'q-pusher-q-pwa');

    $result = q_send_push_notification(
        $token,
        $title,
        $message,
        '', // No image
        true // Debug mode
    );

    if ($result) {
        wp_send_json_success(__('Test notification sent successfully.', 'q-pusher-q-pwa'));
    } else {
        wp_send_json_error(__('Failed to send test notification. Check server logs for details.', 'q-pusher-q-pwa'));
    }
}

// Add AJAX handler for clearing analytics
add_action('wp_ajax_q_clear_analytics', 'q_clear_analytics_callback');

function q_clear_analytics_callback()
{
    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_send_json_error(__('Permission denied.', 'q-pusher-q-pwa'));
        return;
    }

    // Verify nonce
    check_ajax_referer('q_clear_analytics', 'nonce');

    global $wpdb;
    $table_name = $wpdb->prefix . 'q_notification_analytics';

    // Clear the analytics table
    $result = $wpdb->query("TRUNCATE TABLE $table_name");

    if ($result !== false) {
        wp_send_json_success(__('Analytics data cleared successfully.', 'q-pusher-q-pwa'));
    } else {
        wp_send_json_error(__('Failed to clear analytics data.', 'q-pusher-q-pwa'));
    }
}

function q_update_db_check()
{
    if (get_option('q_db_version') != Q_DB_VERSION) {
        Q_Activator::activate();
        update_option('q_db_version', Q_DB_VERSION);
    }
}
add_action('plugins_loaded', 'q_update_db_check');

add_action('admin_menu', function () {
    if (class_exists('Q_Diagnostics')) {
        add_submenu_page(
            'tools.php',
            'Q Pusher Diagnostics',
            'Q Pusher Diagnostics',
            'manage_options',
            'q-pusher-diagnostics',
            ['Q_Diagnostics', 'display_diagnostics_page']
        );
    }
});

/**
 * Verify service worker installation
 */
function q_verify_service_worker()
{
    $sw_path = ABSPATH . 'firebase-messaging-sw.js';
    if (!file_exists($sw_path)) {
        // error_log('Q-Pusher: Service worker not found, attempting to reinstall');
        q_copy_service_worker();
    }
}
add_action('admin_init', 'q_verify_service_worker');

/**
 * Display admin notice for service worker issues
 */
function q_service_worker_admin_notice()
{
    if (!file_exists(ABSPATH . 'firebase-messaging-sw.js')) {
        // Add inline script for AJAX reinstall
        ?>
        <div class="error">
            <p>
                Q-Pusher: The Firebase service worker is not properly installed.
                <a href="<?php echo admin_url('tools.php?page=q-pusher-diagnostics'); ?>">
                    Run diagnostics
                </a>
                or
                <a href="#" id="q-reinstall-service-worker">
                    click here to reinstall
                </a>.
            </p>
            <div id="q-reinstall-result"
                style="display:none; margin-top: 10px; padding: 10px; background-color: #f8f8f8; border-left: 4px solid #46b450;">
            </div>
        </div>
        <script>
            jQuery(document).ready(function ($) {
                $('#q-reinstall-service-worker').on('click', function (e) {
                    e.preventDefault();

                    var $button = $(this);
                    var $result = $('#q-reinstall-result');

                    $button.text('Reinstalling...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'q_reinstall_service_worker',
                            nonce: '<?php echo wp_create_nonce('q_admin_nonce'); ?>'
                        },
                        success: function (response) {
                            if (response.success) {
                                $result.css('border-left-color', '#46b450').html('Service worker reinstalled successfully! Refresh the page to verify.');
                            } else {
                                $result.css('border-left-color', '#dc3232').html('Failed to reinstall service worker. Please check error logs or try the diagnostics page.');
                            }
                            $result.show();
                            $button.text('Click here to reinstall');
                        },
                        error: function () {
                            $result.css('border-left-color', '#dc3232').html('AJAX error occurred. Please try the diagnostics page instead.');
                            $result.show();
                            $button.text('Click here to reinstall');
                        }
                    });
                });
            });
        </script>
        <?php
    }
}
add_action('admin_notices', 'q_service_worker_admin_notice');

/**
 * Add AJAX handler for service worker reinstallation
 */
function q_handle_reinstall_service_worker()
{
    check_ajax_referer('q_admin_nonce', 'nonce');
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
        return;
    }

    $result = q_copy_service_worker();
    wp_send_json_success(['success' => $result]);
}
add_action('wp_ajax_q_reinstall_service_worker', 'q_handle_reinstall_service_worker');

/**
 * Handle AJAX request to verify subscription status
 */
add_action('wp_ajax_q_verify_subscription', 'q_verify_subscription_callback');
add_action('wp_ajax_nopriv_q_verify_subscription', 'q_verify_subscription_callback');

// Add AJAX handler for PWA events tracking
add_action('wp_ajax_q_track_pwa_event', 'q_track_pwa_event_callback');
add_action('wp_ajax_nopriv_q_track_pwa_event', 'q_track_pwa_event_callback');

function q_verify_subscription_callback()
{
    // Verify nonce (accept either 'nonce' or 'security' parameter to improve compatibility)
    $passed_nonce = isset($_POST['nonce']) ? sanitize_text_field($_POST['nonce']) : ( isset($_POST['security']) ? sanitize_text_field($_POST['security']) : '' );

    if ( empty( $passed_nonce ) ) {
        wp_send_json_error( [ 'message' => 'Security token is required' ] );
        return;
    }

    // Verify nonce against correct action
    if ( ! wp_verify_nonce( $passed_nonce, 'q_subscribe_nonce' ) ) {
        wp_send_json_error( [ 'message' => 'Invalid security token' ] );
        return;
    }

    $token = sanitize_text_field($_POST['token']);
    $user_id = get_current_user_id();

    // Get stored token
    $stored_token = get_user_meta($user_id, 'q_push_token', true);

    wp_send_json_success([
        'isValid' => !empty($stored_token) && $stored_token === $token
    ]);
}

/**
 * Handle AJAX request to track PWA events
 */
function q_track_pwa_event_callback()
{
    // Verify nonce
    if (!check_ajax_referer('q_pwa_nonce', 'nonce', false)) {
        wp_send_json_error(['message' => 'Invalid security token']);
        return;
    }

    // Get event data
    $event = sanitize_text_field($_POST['event'] ?? '');
    $data = $_POST['data'] ?? [];

    if (empty($event)) {
        wp_send_json_error(['message' => 'Event name is required']);
        return;
    }

    // Sanitize event data
    $sanitized_data = [];
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $sanitized_data[sanitize_key($key)] = sanitize_text_field($value);
        }
    }

    // Store PWA event in database
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_pwa_analytics';

    $result = $wpdb->insert(
        $table_name,
        [
            'event_name' => $event,
            'event_data' => json_encode($sanitized_data),
            'user_id' => get_current_user_id(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'timestamp' => current_time('mysql')
        ],
        ['%s', '%s', '%d', '%s', '%s', '%s']
    );

    if ($result !== false) {
        wp_send_json_success(['message' => 'Event tracked successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to track event']);
    }
}

function q_validate_notification_payload($payload)
{
    $required_fields = ['title', 'message', 'token'];
    $errors = [];

    // Check required fields
    foreach ($required_fields as $field) {
        if (empty($payload[$field])) {
            $errors[] = "Missing required field: {$field}";
        }
    }

    // Validate token format
    if (!empty($payload['token']) && !preg_match('/^[a-zA-Z0-9:_-]{100,300}$/', $payload['token'])) {
        $errors[] = "Invalid token format";
    }

    // Validate title length
    if (strlen($payload['title']) > 100) {
        $errors[] = "Title exceeds maximum length of 100 characters";
    }

    // Validate message length
    if (strlen($payload['message']) > 2000) {
        $errors[] = "Message exceeds maximum length of 2000 characters";
    }

    // Validate image URL if present
    if (!empty($payload['image']) && !filter_var($payload['image'], FILTER_VALIDATE_URL)) {
        $errors[] = "Invalid image URL format";
    }

    return empty($errors) ? true : $errors;
}

function q_sanitize_input($data, $type = 'text')
{
    if (is_array($data)) {
        return array_map(function ($item) use ($type) {
            return q_sanitize_input($item, $type);
        }, $data);
    }

    $data = trim($data);

    switch ($type) {
        case 'email':
            return filter_var(sanitize_email($data), FILTER_VALIDATE_EMAIL) ? sanitize_email($data) : '';
        case 'url':
            return filter_var(esc_url_raw($data), FILTER_VALIDATE_URL) ? esc_url_raw($data) : '';
        case 'textarea':
            return wp_kses(sanitize_textarea_field($data), [
                'a' => ['href' => [], 'title' => []],
                'br' => [],
                'em' => [],
                'strong' => []
            ]);
        case 'key':
            return preg_replace('/[^a-zA-Z0-9_-]/', '', sanitize_key($data));
        case 'html':
            return wp_kses_post($data);
        case 'int':
            return filter_var($data, FILTER_VALIDATE_INT) !== false ? intval($data) : 0;
        case 'float':
            return filter_var($data, FILTER_VALIDATE_FLOAT) !== false ? floatval($data) : 0.0;
        case 'boolean':
            return filter_var($data, FILTER_VALIDATE_BOOLEAN);
        default:
            return sanitize_text_field($data);
    }
}

/**
 * Get the number of devices registered for a user
 *
 * @param int $user_id The user ID
 * @return int Number of registered devices
 */
function q_get_user_device_count($user_id)
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'q_devices';

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
        $user_id
    ));

    return (int) $count;
}




// Add a shortcode to display a Push-Notification "Inbox" so users can review recent messages.
if ( ! function_exists( 'q_notification_inbox_shortcode' ) ) {
    /**
     * Render a list of recently sent push notifications.
     *
     * Usage: [q_notification_inbox limit="15"]
     *
     * @param array $atts Optional shortcode attributes.
     * @return string HTML markup for the inbox.
     */
    function q_notification_inbox_shortcode( $atts = [] ) {
        // Parse shortcode attributes with defaults.
        $atts = shortcode_atts( [
            'limit' => 20, // Number of notifications visible before scrolling.
        ], $atts, 'q_notification_inbox' );

        $visible_limit = absint( $atts['limit'] );
        if ( $visible_limit <= 0 ) {
            $visible_limit = 20;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'q_notification_analytics';

        // Begin user-scoped filtering
        $user_id = get_current_user_id();
        if ( ! $user_id) {
            return '';
        }

        // Get the user's primary push-token (stored by the subscribe callback)
        $user_token = get_user_meta( $user_id, 'q_push_token', true );
        $is_subscribed = !empty($user_token);
        // Analytics rows may store only the first 10 chars of the token + an ellipsis for privacy
        $token_prefix = $is_subscribed ? substr( $user_token, 0, 10 ) . '...' : '';

        // Retrieve the most recent "sent" notification analytics rows *for this token*
        $rows = $is_subscribed ? $wpdb->get_results( $wpdb->prepare(
            "SELECT id, data, timestamp FROM {$table} WHERE type = %s AND data LIKE %s ORDER BY timestamp DESC LIMIT 100",
            'sent', '%' . $wpdb->esc_like( $token_prefix ) . '%'
        ) ) : [];

        // Calculate max-height for scroll container (approx 110px per item)
        $max_height = ( $visible_limit * 110 ) + 60; // safety padding
        $has_notifications = !empty($rows);

        ob_start();
        ?>
        <div class="q-notification-inbox" style="max-height: <?php echo intval( $max_height ); ?>px; overflow-y: auto;">
            <div class="q-notification-toolbar">
                <span class="q-notification-heading">Notifications</span>
                <?php if ($is_subscribed && $has_notifications): ?>
                    <button id="q-notification-clear-all" class="q-clear-all" title="Clear all">Clear All</button>
                <?php endif; ?>
            </div>
            <div class="q-notification-list">
                <?php if ( !$is_subscribed ) : ?>
                    <div class="q-notification-empty-state">
                        <div class="q-empty-art">
                            <svg width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="30" cy="30" r="24" fill="#9BD7FF"/>
                                <circle cx="60" cy="30" r="24" fill="#C8E7FF"/>
                                <circle cx="90" cy="30" r="24" fill="#3A7EF5"/>
                            </svg>
                        </div>
                        <h3><?php esc_html_e('Notifications are currently disabled', 'q-pusher-q-pwa'); ?></h3>
                        <p><?php esc_html_e('Turn on notifications to stay updated in real time.', 'q-pusher-q-pwa'); ?></p>
                        <div style="margin-top: 18px;">
                            <?php echo do_shortcode('[q_subscribe_button text="Enable Notifications" class="ui-button primary"]'); ?>
                        </div>
                    </div>
                <?php elseif ( $has_notifications ) : ?>
                    <?php foreach ( $rows as $row ) :
                        $data = json_decode( $row->data, true );
                        if ( ! is_array( $data ) ) {
                            continue;
                        }

                        $title     = isset( $data['title'] ) ? esc_html( $data['title'] ) : '';
                        $message   = isset( $data['message'] ) ? esc_html( $data['message'] ) : ( isset( $data['body'] ) ? esc_html( $data['body'] ) : '' );
                        $icon      = isset( $data['icon'] ) ? esc_url( $data['icon'] ) : '';
                        $time_ago  = sprintf( '%s ago', human_time_diff( strtotime( $row->timestamp ), current_time( 'timestamp' ) ) );

                        // Determine primary media (large image, image, video thumbnail, gif)
                        $media_url = '';
                        if ( ! empty( $data['large_image'] ) ) {
                            $media_url = esc_url( $data['large_image'] );
                        } elseif ( ! empty( $data['image'] ) ) {
                            $media_url = esc_url( $data['image'] );
                        } elseif ( ! empty( $data['video_thumbnail'] ) ) {
                            $media_url = esc_url( $data['video_thumbnail'] );
                        } elseif ( ! empty( $data['gif_url'] ) ) {
                            $media_url = esc_url( $data['gif_url'] );
                        }

                        $audio_url = ! empty( $data['audio_url'] ) ? esc_url( $data['audio_url'] ) : '';
                        $actions   = ! empty( $data['actions'] ) && is_array( $data['actions'] ) ? $data['actions'] : [];
                        ?>
                        <div class="q-notification-item">
                            <?php if ( $icon ) : ?>
                                <img class="q-notification-avatar" src="<?php echo $icon; ?>" alt="" />
                            <?php endif; ?>

                            <div class="q-notification-text">
                                <strong class="q-notification-title"><?php echo $title; ?></strong>
                                <span class="q-notification-body"><?php echo $message; ?></span>

                                <?php if ( $media_url ) : ?>
                                    <img class="q-notification-media" src="<?php echo $media_url; ?>" alt="media" />
                                <?php endif; ?>

                                <?php if ( $audio_url ) : ?>
                                    <audio class="q-notification-audio" controls src="<?php echo $audio_url; ?>"></audio>
                                <?php endif; ?>

                                <?php if ( $actions ) : ?>
                                    <div class="q-notification-actions">
                                        <?php foreach ( $actions as $btn ) :
                                            $btn_title = isset( $btn['title'] ) ? esc_html( $btn['title'] ) : '';
                                            $btn_url   = isset( $btn['url'] ) ? esc_url( $btn['url'] ) : '#';
                                        ?>
                                            <a class="q-notification-btn" href="<?php echo $btn_url; ?>" target="_blank" rel="noopener noreferrer"><?php echo $btn_title; ?></a>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                <span class="q-notification-time"><?php echo esc_html( $time_ago ); ?></span>
                            </div>

                  
                            <button class="q-notification-clear" data-id="<?php echo esc_attr( $row->id ); ?>" title="Remove">×</button>
                        </div>
                    <?php endforeach; ?>
                <?php else : ?>
                    <div class="q-notification-empty-state">
                        <div class="q-empty-art">
                            <!-- Simple SVG bubbles -->
                            <svg width="120" height="60" viewBox="0 0 120 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="30" cy="30" r="24" fill="#9BD7FF"/>
                                <circle cx="60" cy="30" r="24" fill="#C8E7FF"/>
                                <circle cx="90" cy="30" r="24" fill="#3A7EF5"/>
                            </svg>
                        </div>
                        <h3>No new notifications</h3>
                        <p>Looks like you haven't received any notifications.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <style>
            .q-notification-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                border-bottom: 1px solid #f1f1f1;
                background: #fafafa;
                position: sticky;
                top: 0;
                z-index: 2;
            }
            .q-notification-heading {
                font-weight: 600;
                color: #000;
            }
            .q-clear-all {
                background: transparent;
                border: none;
                color: #d11a2a;
                cursor: pointer;
                font-size: 0.9rem;
            }
            .q-clear-all[disabled] {
                opacity: 0.5;
                cursor: not-allowed;
            }
            .q-notification-inbox {
                width: 100%;
                max-width: 460px;
                background: #ffffff;
                border-radius: 12px;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            }
            .q-notification-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 16px 20px;
                border-bottom: 1px solid #f1f1f1;
                position: relative;
            }
            .q-notification-item:last-child {
                border-bottom: none;
            }
            .q-notification-avatar {
                width: 40px;
                height: 40px !important;
                border-radius: 50% !important;
                object-fit: cover !important;
                flex-shrink: 0;
            }
            .q-notification-text {
                flex: 1;
            }
            .q-notification-title {
                display: block;
                color: #000;
                margin-bottom: 2px;
            }
            .q-notification-body {
                display: block;
                color: #333333;
                font-size: 0.95rem;
            }
            .q-notification-time {
                color: #888888;
                font-size: 0.8rem;
                white-space: nowrap;
                margin-left: 8px;
            }
            .q-notification-clear {
                position: absolute;
                top: 10px;
                right: 10px;
                background: transparent;
                border: none;
                font-size: 16px;
                line-height: 1;
                color: #999;
                cursor: pointer;
            }
            .q-notification-clear:hover {
                color: #d11a2a;
            }
            .q-notification-empty-state {
                text-align: center;
                padding: 40px 20px;
                margin: 0 auto;
            }
            .q-empty-art svg {
                display: block;
                margin: 0 auto 12px;
            }
            .q-notification-empty-state h3 {
                margin: 0 0 6px;
                font-size: 1.1rem;
                color: #0d1a26;
            }
            .q-notification-empty-state p {
                margin: 0;
                color: #6b7c93;
                font-size: 0.9rem;
            }
            .q-notification-media {
                width: 100%;
                max-height: 180px;
                object-fit: cover;
                border-radius: 8px;
                margin: 8px 0;
            }
            .q-notification-audio {
                width: 100%;
                margin-top: 6px;
            }
            .q-notification-actions {
                margin-top: 8px;
            }
            .q-notification-btn {
                display: inline-block;
                margin-right: 6px;
                margin-bottom: 4px;
                padding: 6px 12px;
                background: #007cba;
                color: #fff;
                font-size: 0.8rem;
                border-radius: 4px;
                text-decoration: none;
            }
            .q-notification-btn:hover {
                background: #0064a5;
            }
        </style>
        <?php
        // Nonce for client-side requests.
        $nonce = wp_create_nonce( 'q_notification_inbox_nonce' );

        // Inline JS to handle deletions.
        ?>
        <script>
            (function(){
                const ajaxUrl = <?php echo json_encode( admin_url('admin-ajax.php') ); ?>;
                const nonce   = <?php echo json_encode( $nonce ); ?>;

                // Remove a DOM element helper
                const emptyHTML = `<div class=\"q-notification-empty-state\"><div class=\"q-empty-art\"><svg width=\"120\" height=\"60\" viewBox=\"0 0 120 60\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"30\" cy=\"30\" r=\"24\" fill=\"#9BD7FF\"/><circle cx=\"60\" cy=\"30\" r=\"24\" fill=\"#C8E7FF\"/><circle cx=\"90\" cy=\"30\" r=\"24\" fill=\"#3A7EF5\"/></svg></div><h3>No new notifications</h3><p>Looks like you haven't received any notifications.</p></div>`;

                function checkAndInjectEmpty(){
                    const list = document.querySelector('.q-notification-list');
                    if(list && !document.querySelector('.q-notification-item')){
                        if(!document.querySelector('.q-notification-empty-state')){
                            list.innerHTML = emptyHTML;
                        }
                        // Disable clear all
                        const clearAll = document.getElementById('q-notification-clear-all');
                        if(clearAll){ clearAll.setAttribute('disabled', 'disabled'); clearAll.style.opacity = '0.5'; clearAll.style.cursor = 'not-allowed'; }
                    }
                }

                function fadeOutAndRemove(el){
                    el.style.opacity = '0';
                    setTimeout(()=>{ el.remove(); checkAndInjectEmpty(); }, 300);
                }

                // Handle individual clear
                document.addEventListener('click', function(e){
                    if(e.target.matches('.q-notification-clear')){
                        e.preventDefault();
                        const row   = e.target.closest('.q-notification-item');
                        const id    = e.target.getAttribute('data-id');

                        fetch(ajaxUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: new URLSearchParams({
                                action: 'q_delete_notification',
                                id: id,
                                nonce: nonce
                            })
                        }).then(r=>r.json()).then(resp=>{
                            if(resp.success){
                                fadeOutAndRemove(row);
                            } else { console.error(resp.data); }
                        });
                    }
                });

                // Handle clear all
                document.addEventListener('click', function(e){
                    if(e.target.matches('#q-notification-clear-all') && !e.target.hasAttribute('disabled')){
                        e.preventDefault();
                        if(!confirm('Clear all notifications?')) return;
                        fetch(ajaxUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: new URLSearchParams({
                                action: 'q_delete_all_notifications',
                                nonce: nonce
                            })
                        }).then(r=>r.json()).then(resp=>{
                            if(resp.success){
                                document.querySelectorAll('.q-notification-item').forEach(fadeOutAndRemove);
                                setTimeout(checkAndInjectEmpty, 350);
                            } else { console.error(resp.data); }
                        });
                    }
                });
            })();
        </script>
        <?php
        return ob_get_clean();
    }
}
add_shortcode( 'q_notification_inbox', 'q_notification_inbox_shortcode' );

// -----------------------------------------------------------------------------
// AJAX handlers for deleting notifications
// -----------------------------------------------------------------------------

if ( ! function_exists( 'q_delete_notification_callback' ) ) {
    function q_delete_notification_callback() {
        // Basic permission: require logged-in user.
        if ( ! is_user_logged_in() ) {
            wp_send_json_error( 'Permission denied' );
        }

        // Nonce check
        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( $_POST['nonce'] ), 'q_notification_inbox_nonce' ) ) {
            wp_send_json_error( 'Invalid security token' );
        }

        if ( empty( $_POST['id'] ) || ! is_numeric( $_POST['id'] ) ) {
            wp_send_json_error( 'Invalid ID' );
        }

        global $wpdb;
        $table = $wpdb->prefix . 'q_notification_analytics';
        $id    = intval( $_POST['id'] );

        $deleted = $wpdb->delete( $table, [ 'id' => $id ], [ '%d' ] );

        if ( false === $deleted ) {
            wp_send_json_error( 'Database error' );
        }

        wp_send_json_success();
    }
}
add_action( 'wp_ajax_q_delete_notification', 'q_delete_notification_callback' );

if ( ! function_exists( 'q_delete_all_notifications_callback' ) ) {
    function q_delete_all_notifications_callback() {
        // Only logged-in users with manage_options capability can clear all.
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( 'Permission denied' );
        }

        if ( ! isset( $_POST['nonce'] ) || ! wp_verify_nonce( sanitize_text_field( $_POST['nonce'] ), 'q_notification_inbox_nonce' ) ) {
            wp_send_json_error( 'Invalid security token' );
        }

        global $wpdb;
        $table = $wpdb->prefix . 'q_notification_analytics';
        $deleted = $wpdb->query( "DELETE FROM {$table} WHERE type = 'sent'" );

        if ( false === $deleted ) {
            wp_send_json_error( 'Database error' );
        }

        wp_send_json_success();
    }
}
add_action( 'wp_ajax_q_delete_all_notifications', 'q_delete_all_notifications_callback' );

// Enqueue Bootstrap and Bootstrap Icons for the plugin admin and frontend
function q_pusher_enqueue_bootstrap() {
    // Bootstrap CSS
    wp_enqueue_style(
        'bootstrap-css',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css',
        array(),
        '5.3.3'
    );

    // Bootstrap Icons
    wp_enqueue_style(
        'bootstrap-icons',
        'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css',
        array(),
        '1.11.3'
    );

    // Bootstrap JS (optional, only if you need JS features)
    wp_enqueue_script(
        'bootstrap-js',
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js',
        array('jquery'),
        '5.3.3',
        true
    );
}
add_action('wp_enqueue_scripts', 'q_pusher_enqueue_bootstrap'); // For frontend
add_action('admin_enqueue_scripts', 'q_pusher_enqueue_bootstrap'); // For admin
