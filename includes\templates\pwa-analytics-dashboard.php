<?php
/**
 * PWA Analytics Dashboard Template
 * 
 * Displays analytics data for the PWA in an admin dashboard format
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get analytics data
$period = isset($_GET['period']) ? sanitize_text_field($_GET['period']) : 'week';
$valid_periods = ['today', 'yesterday', 'week', 'month', 'all'];
if (!in_array($period, $valid_periods)) {
    $period = 'week';
}

$analytics_data = q_get_pwa_analytics($period);
$total_events = 0;
foreach ($analytics_data as $event) {
    $total_events += $event['count'];
}

// Get chart data for installations
$chart_data = q_get_pwa_analytics_chart_data('install', $period === 'today' ? 'day' : $period);

// --- New Insights ---
// Average session duration (in seconds)
global $wpdb;
$table_name = $wpdb->prefix . 'q_pwa_analytics';
$avg_session_duration = $wpdb->get_var("SELECT AVG(TIMESTAMPDIFF(SECOND, session_start, session_end)) FROM {$table_name} WHERE event_name = 'session_end'" . ($period !== 'all' ? " AND " . ($period === 'today' ? 'DATE(timestamp) = CURDATE()' : ($period === 'yesterday' ? 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)' : ($period === 'week' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)' : ($period === 'month' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)' : ''))) ) : ''));

// Most visited page
$most_visited_page = $wpdb->get_var("SELECT event_data->>'$.url' as url FROM {$table_name} WHERE event_name = 'pageview'" . ($period !== 'all' ? " AND " . ($period === 'today' ? 'DATE(timestamp) = CURDATE()' : ($period === 'yesterday' ? 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)' : ($period === 'week' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)' : ($period === 'month' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)' : ''))) ) : '') . " GROUP BY url ORDER BY COUNT(*) DESC LIMIT 1");

// Device type breakdown
$device_breakdown = $wpdb->get_results("SELECT device_type, COUNT(id) as count FROM {$table_name} WHERE device_type IS NOT NULL AND device_type != ''" . ($period !== 'all' ? " AND " . ($period === 'today' ? 'DATE(timestamp) = CURDATE()' : ($period === 'yesterday' ? 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)' : ($period === 'week' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)' : ($period === 'month' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)' : ''))) ) : '') . " GROUP BY device_type ORDER BY count DESC", ARRAY_A);

// Push notifications enabled count
$push_enabled_count = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM {$table_name} WHERE event_name = 'push_subscribed'" . ($period !== 'all' ? " AND " . ($period === 'today' ? 'DATE(timestamp) = CURDATE()' : ($period === 'yesterday' ? 'DATE(timestamp) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)' : ($period === 'week' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)' : ($period === 'month' ? 'timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)' : ''))) ) : ''));

// Currently online users (last 5 minutes)
$online_users = $wpdb->get_var("SELECT COUNT(DISTINCT user_id) FROM {$table_name} WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)");

// Advanced stats
$session_hist = class_exists('Q_PWA_Analytics') ? Q_PWA_Analytics::get_session_length_histogram() : [];
$top_browsers = class_exists('Q_PWA_Analytics') ? Q_PWA_Analytics::get_top_browsers() : [];
$top_os = class_exists('Q_PWA_Analytics') ? Q_PWA_Analytics::get_top_os() : [];
$notif_engagement = class_exists('Q_PWA_Analytics') ? Q_PWA_Analytics::get_notification_engagement() : [];
$retention = class_exists('Q_PWA_Analytics') ? Q_PWA_Analytics::get_retention_cohorts() : [];
?>

<div class="q-pwa-analytics-dashboard">
    <div class="q-pwa-analytics-header">
        <h2><?php _e('PWA Analytics Dashboard', 'q-pusher-q-pwa'); ?></h2>
        <div class="q-pwa-analytics-period-selector">
            <form method="get">
                <input type="hidden" name="page" value="q-pwa-settings">
                <input type="hidden" name="tab" value="analytics">
                <select name="period" onchange="this.form.submit()">
                    <option value="today" <?php selected($period, 'today'); ?>><?php _e('Today', 'q-pusher-q-pwa'); ?></option>
                    <option value="yesterday" <?php selected($period, 'yesterday'); ?>><?php _e('Yesterday', 'q-pusher-q-pwa'); ?></option>
                    <option value="week" <?php selected($period, 'week'); ?>><?php _e('Last 7 Days', 'q-pusher-q-pwa'); ?></option>
                    <option value="month" <?php selected($period, 'month'); ?>><?php _e('Last 30 Days', 'q-pusher-q-pwa'); ?></option>
                    <option value="all" <?php selected($period, 'all'); ?>><?php _e('All Time', 'q-pusher-q-pwa'); ?></option>
                </select>
            </form>
        </div>
    </div>

    <div class="q-pwa-analytics-summary">
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Total Events', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value"><?php echo number_format_i18n($total_events); ?></div>
            </div>
        </div>
        
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Installations', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value">
                    <?php 
                    $installs = 0;
                    foreach ($analytics_data as $event) {
                        if ($event['event_name'] === 'install') {
                            $installs = $event['count'];
                            break;
                        }
                    }
                    echo number_format_i18n($installs); 
                    ?>
                </div>
            </div>
        </div>
        
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('App Launches', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value">
                    <?php 
                    $launches = 0;
                    foreach ($analytics_data as $event) {
                        if ($event['event_name'] === 'appstart') {
                            $launches = $event['count'];
                            break;
                        }
                    }
                    echo number_format_i18n($launches); 
                    ?>
                </div>
            </div>
        </div>
        
       
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Avg. Session Duration', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value">
                    <?php echo $avg_session_duration ? gmdate('H:i:s', $avg_session_duration) : __('N/A', 'q-pusher-q-pwa'); ?>
                </div>
            </div>
        </div>
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Most Visited Page', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value" style="font-size:14px;word-break:break-all;">
                    <?php echo $most_visited_page ? esc_html($most_visited_page) : __('N/A', 'q-pusher-q-pwa'); ?>
                </div>
            </div>
        </div>
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Push Enabled Users', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value">
                    <?php echo number_format_i18n($push_enabled_count); ?>
                </div>
            </div>
        </div>
        <div class="q-pwa-analytics-card">
            <div class="q-pwa-analytics-card-header">
                <h3><?php _e('Currently Online Users', 'q-pusher-q-pwa'); ?></h3>
            </div>
            <div class="q-pwa-analytics-card-body">
                <div class="q-pwa-analytics-card-value">
                    <?php echo number_format_i18n($online_users); ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($chart_data)): ?>
    <div class="q-pwa-analytics-chart-container">
        <h3><?php _e('Installation Trend', 'q-pusher-q-pwa'); ?></h3>
        <div class="q-pwa-analytics-chart" id="installationsChart">
            <!-- Chart will be rendered here by JavaScript -->
            <div class="q-pwa-chart-placeholder">
                <?php foreach ($chart_data as $data_point): ?>
                <div class="q-pwa-chart-bar" style="height: <?php echo min(100, max(5, ($data_point['count'] / max(1, max(array_column($chart_data, 'count'))) * 100))); ?>%;" title="<?php echo esc_attr($data_point['label'] . ': ' . $data_point['count']); ?>">
                    <span class="q-pwa-chart-label"><?php echo esc_html($data_point['label']); ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="q-pwa-analytics-table-container">
        <h3><?php _e('Event Breakdown', 'q-pusher-q-pwa'); ?></h3>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Event', 'q-pusher-q-pwa'); ?></th>
                    <th><?php _e('Count', 'q-pusher-q-pwa'); ?></th>
                    <th><?php _e('Percentage', 'q-pusher-q-pwa'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($analytics_data)): ?>
                <tr>
                    <td colspan="3"><?php _e('No analytics data available for the selected period.', 'q-pusher-q-pwa'); ?></td>
                </tr>
                <?php else: ?>
                    <?php foreach ($analytics_data as $event): ?>
                    <tr>
                        <td><?php echo esc_html(ucfirst($event['event_name'])); ?></td>
                        <td><?php echo number_format_i18n($event['count']); ?></td>
                        <td><?php echo round(($event['count'] / max(1, $total_events)) * 100, 1); ?>%</td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Device Breakdown Table -->
    <div class="q-pwa-analytics-table-container">
        <h3><?php _e('Device Type Breakdown', 'q-pusher-q-pwa'); ?></h3>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Device Type', 'q-pusher-q-pwa'); ?></th>
                    <th><?php _e('Count', 'q-pusher-q-pwa'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($device_breakdown)): ?>
                <tr>
                    <td colspan="2"><?php _e('No device data available for the selected period.', 'q-pusher-q-pwa'); ?></td>
                </tr>
                <?php else: ?>
                    <?php foreach ($device_breakdown as $device): ?>
                    <tr>
                        <td><?php echo esc_html(ucfirst($device['device_type'])); ?></td>
                        <td><?php echo number_format_i18n($device['count']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Device OS Breakdown Table (future: update tracking to include OS info) -->
    <div class="q-pwa-analytics-table-container">
        <h3><?php _e('Device OS Breakdown', 'q-pusher-q-pwa'); ?></h3>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Device OS (proxy)', 'q-pusher-q-pwa'); ?></th>
                    <th><?php _e('Count', 'q-pusher-q-pwa'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($device_breakdown)): ?>
                <tr>
                    <td colspan="2"><?php _e('No device data available for the selected period.', 'q-pusher-q-pwa'); ?></td>
                </tr>
                <?php else: ?>
                    <?php foreach ($device_breakdown as $device): ?>
                    <tr>
                        <td><?php echo esc_html(ucfirst($device['device_type'])); // In future, use OS ?></td>
                        <td><?php echo number_format_i18n($device['count']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <p style="font-size:12px;color:#888;">Note: OS breakdown uses device_type as a proxy. For true OS stats, update tracking to send OS info (iOS, Android, etc.).</p>
    </div>

    <div class="q-pwa-analytics-export">
        <button class="button" id="exportAnalyticsCSV"><?php _e('Export to CSV', 'q-pusher-q-pwa'); ?></button>
        <button class="button" id="refreshAnalytics"><?php _e('Refresh Data', 'q-pusher-q-pwa'); ?></button>
    </div>

    <!-- Advanced Insights Section -->
    <div class="q-pwa-analytics-advanced" style="margin-top:40px;">
        <h2 style="font-size:1.3em;color:#0073aa;margin-bottom:18px;">Advanced Insights</h2>
        <div style="display:flex;flex-wrap:wrap;gap:32px;align-items:stretch;">
            <!-- Session Length Histogram -->
            <div style="flex:1 1 260px;min-width:220px;background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,0.04);padding:18px 20px 14px 20px;">
                <h4 style="margin-top:0;">Session Length Distribution</h4>
                <?php if (!empty($session_hist)): ?>
                    <ul style="padding-left:0;list-style:none;">
                        <?php foreach ($session_hist as $label => $count): ?>
                            <li style="margin-bottom:6px;"><b><?php echo esc_html($label); ?>:</b> <?php echo number_format_i18n($count); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p style="color:#888;">No session data.</p>
                <?php endif; ?>
            </div>
            <!-- Top Browsers -->
            <div style="flex:1 1 220px;min-width:180px;background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,0.04);padding:18px 20px 14px 20px;">
                <h4 style="margin-top:0;">Top Browsers</h4>
                <?php if (!empty($top_browsers)): ?>
                    <ul style="padding-left:0;list-style:none;">
                        <?php foreach ($top_browsers as $row): ?>
                            <li style="margin-bottom:6px;"><b><?php echo esc_html($row['browser']); ?>:</b> <?php echo number_format_i18n($row['count']); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p style="color:#888;">No browser data.</p>
                <?php endif; ?>
            </div>
            <!-- Top OS -->
            <div style="flex:1 1 220px;min-width:180px;background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,0.04);padding:18px 20px 14px 20px;">
                <h4 style="margin-top:0;">Top Operating Systems</h4>
                <?php if (!empty($top_os)): ?>
                    <ul style="padding-left:0;list-style:none;">
                        <?php foreach ($top_os as $row): ?>
                            <li style="margin-bottom:6px;"><b><?php echo esc_html($row['os']); ?>:</b> <?php echo number_format_i18n($row['count']); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p style="color:#888;">No OS data.</p>
                <?php endif; ?>
            </div>
            <!-- Notification Engagement -->
            <div style="flex:1 1 220px;min-width:180px;background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,0.04);padding:18px 20px 14px 20px;">
                <h4 style="margin-top:0;">Notification Engagement</h4>
                <?php if (!empty($notif_engagement)): ?>
                    <ul style="padding-left:0;list-style:none;">
                        <li><b>Sent:</b> <?php echo number_format_i18n($notif_engagement['sent']); ?></li>
                        <li><b>Clicked:</b> <?php echo number_format_i18n($notif_engagement['clicked']); ?></li>
                        <li><b>Dismissed:</b> <?php echo number_format_i18n($notif_engagement['dismissed']); ?></li>
                    </ul>
                <?php else: ?>
                    <p style="color:#888;">No notification data.</p>
                <?php endif; ?>
            </div>
            <!-- Retention Cohorts -->
            <div style="flex:1 1 220px;min-width:180px;background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,0.04);padding:18px 20px 14px 20px;">
                <h4 style="margin-top:0;">Retention Cohorts</h4>
                <?php if (!empty($retention)): ?>
                    <ul style="padding-left:0;list-style:none;">
                        <li><b>Returned after 1 day:</b> <?php echo number_format_i18n($retention['1d']); ?></li>
                        <li><b>Returned after 7 days:</b> <?php echo number_format_i18n($retention['7d']); ?></li>
                        <li><b>Returned after 30 days:</b> <?php echo number_format_i18n($retention['30d']); ?></li>
                    </ul>
                <?php else: ?>
                    <p style="color:#888;">No retention data.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Improved Analytics Reference Section -->
    <style>
    .q-pwa-analytics-reference {
        background: #f9fbfd;
        border: 1px solid #e1e8ed;
        padding: 28px 24px 24px 24px;
        margin-bottom: 36px;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        display: flex;
        flex-wrap: wrap;
        gap: 32px;
        align-items: flex-start;
    }
    .q-pwa-analytics-reference h3 {
        margin-top: 0;
        margin-bottom: 18px;
        font-size: 1.4em;
        color: #0073aa;
        letter-spacing: 0.5px;
    }
    .q-pwa-analytics-ref-col {
        flex: 1 1 320px;
        min-width: 280px;
    }
    .q-pwa-analytics-ref-list {
        list-style: none;
        padding: 0;
        margin: 0 0 1em 0;
    }
    .q-pwa-analytics-ref-list li {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 14px;
        font-size: 1.05em;
        background: #fff;
        border-radius: 6px;
        padding: 10px 14px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.03);
        border-left: 4px solid #0073aa;
    }
    .q-pwa-analytics-ref-list li:last-child { margin-bottom: 0; }
    {
        font-size: 1.2em;
        color: #0073aa;
        margin-top: 2px;
        min-width: 22px;
        text-align: center;
    }
    .q-pwa-analytics-ref-label {
        font-weight: 500;
        color: #222;
    }
    .q-pwa-analytics-ref-code {
        background: #f1f3f6;
        color: #333;
        border-radius: 4px;
        padding: 2px 7px;
        font-size: 0.98em;
        margin-left: 6px;
    }
    .q-pwa-analytics-ref-note {
        font-size: 12px;
        color: #888;
        margin-top: 10px;
    }
    @media (max-width: 800px) {
        .q-pwa-analytics-reference { flex-direction: column; gap: 18px; }
    }
    </style>
    <div class="q-pwa-analytics-reference">
        <div class="q-pwa-analytics-ref-col">
            <h3> Holistic Stats</h3>
            <ul class="q-pwa-analytics-ref-list">
                <li><span class="q-pwa-analytics-ref-label">Number of Downloads</span><span class="q-pwa-analytics-ref-code">event_type="install"</span></li>
                <li><span class="q-pwa-analytics-ref-label">Number of Subscribers</span><span class="q-pwa-analytics-ref-code">event_type="push_subscribed"</span></li>
                <li><span class="q-pwa-analytics-ref-label">Currently Online Users</span><span class="q-pwa-analytics-ref-code">event_type="online"</span></li>
                <li><span class="q-pwa-analytics-ref-label">Average Session Duration</span><span class="q-pwa-analytics-ref-code">event_type="session_end"</span></li>
                <li><span class="q-pwa-analytics-ref-label">Most Visited Page</span><span class="q-pwa-analytics-ref-code">event_type="pageview"</span></li>
                <li><span class="q-pwa-analytics-ref-label">Users by Device</span><span class="q-pwa-analytics-ref-code">device_type="mobile"/"tablet"/"desktop"</span></li>
            </ul>
        </div>
        <div class="q-pwa-analytics-ref-col">
            <h3> User-Specific Stats</h3>
            <ul class="q-pwa-analytics-ref-list">
                <li><span class="q-pwa-analytics-ref-label">Number of Devices</span><span class="q-pwa-analytics-ref-code">devices</span></li>
                <li><span class="q-pwa-analytics-ref-label">Last Online</span><span class="q-pwa-analytics-ref-code">last_online</span></li>
                <li><span class="q-pwa-analytics-ref-label">Average Session Duration</span><span class="q-pwa-analytics-ref-code">avg_session_duration</span></li>
                <li><span class="q-pwa-analytics-ref-label">Most Visited Page</span><span class="q-pwa-analytics-ref-code">most_visited</span></li>
                <li><span class="q-pwa-analytics-ref-label">Push Notifications Enabled</span><span class="q-pwa-analytics-ref-code">push_enabled</span></li>
                <li><span class="q-pwa-analytics-ref-label">Devices Subscribed To</span><span class="q-pwa-analytics-ref-code">device_list</span></li>
            </ul>
        </div>
        <div style="flex-basis:100%;"></div>
        <div class="q-pwa-analytics-ref-note">
            <b>Note:</b> For currently online users, use the dashboard card or custom query for users active in the last 5 minutes. For device OS, update tracking to send <code>os</code> info for more granular stats.<br>
            See documentation for shortcode usage: <code>[pwa_analytics event_type="install" period="week" format="number"]</code> or <code>[pwa_user_analytics user_id="USER_ID" event="connection_online"]</code>.
        </div>
    </div>
</div>

<style>
.q-pwa-analytics-dashboard {
    margin: 20px 0;
}

.q-pwa-analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.q-pwa-analytics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.q-pwa-analytics-card {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 15px;
    text-align: center;
}

.q-pwa-analytics-card-header h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #555;
}

.q-pwa-analytics-card-value {
    font-size: 28px;
    font-weight: bold;
    color: #0073aa;
}

.q-pwa-analytics-chart-container {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.q-pwa-analytics-chart {
    height: 300px;
    margin-top: 20px;
}

.q-pwa-chart-placeholder {
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    height: 250px;
    padding-top: 20px;
}

.q-pwa-chart-bar {
    width: 30px;
    background: #0073aa;
    border-radius: 3px 3px 0 0;
    position: relative;
    transition: height 0.3s ease;
}

.q-pwa-chart-label {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: #555;
}

.q-pwa-analytics-table-container {
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.q-pwa-analytics-export {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}
</style>

<script>
(function($) {
    $(document).ready(function() {
        // Export to CSV functionality
        $('#exportAnalyticsCSV').on('click', function() {
            let csvContent = "data:text/csv;charset=utf-8,Event,Count,Percentage\n";
            
            <?php if (!empty($analytics_data)): ?>
            <?php foreach ($analytics_data as $event): ?>
            csvContent += "<?php echo $event['event_name']; ?>,<?php echo $event['count']; ?>,<?php echo round(($event['count'] / max(1, $total_events)) * 100, 1); ?>%\n";
            <?php endforeach; ?>
            <?php endif; ?>
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "pwa_analytics_<?php echo $period; ?>.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
        
        // Refresh data
        $('#refreshAnalytics').on('click', function() {
            location.reload();
        });
    });
})(jQuery);
</script>