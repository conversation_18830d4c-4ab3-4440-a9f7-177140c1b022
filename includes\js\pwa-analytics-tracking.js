/**
 * PWA Analytics Tracking Script
 * 
 * Tracks PWA usage events and sends them to the server
 */
(function () {
    'use strict';

    // Check if analytics is enabled
    if (!qPWASettings || !qPWASettings.analyticsEnabled) {
        return;
    }

    // Generate a unique user ID if not already set
    function getUserId() {
        let userId = localStorage.getItem('q_pwa_user_id');
        if (!userId) {
            userId = 'user_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('q_pwa_user_id', userId);
        }
        return userId;
    }

    // Get device type
    function getDeviceType() {
        const ua = navigator.userAgent;
        if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
            return 'tablet';
        }
        if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(ua)) {
            return 'mobile';
        }
        return 'desktop';
    }

    // Add OS, browser, and app version detection
    function getOS() {
        const ua = navigator.userAgent;
        if (/android/i.test(ua)) return 'Android';
        if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) return 'iOS';
        if (/Win/.test(ua)) return 'Windows';
        if (/Mac/.test(ua)) return 'Mac';
        if (/Linux/.test(ua)) return 'Linux';
        return 'Other';
    }
    function getBrowser() {
        const ua = navigator.userAgent;
        if (/chrome|crios|crmo/i.test(ua)) return 'Chrome';
        if (/firefox|fxios/i.test(ua)) return 'Firefox';
        if (/safari/i.test(ua) && !/chrome|crios|crmo/i.test(ua)) return 'Safari';
        if (/edg/i.test(ua)) return 'Edge';
        if (/opr\//i.test(ua)) return 'Opera';
        return 'Other';
    }
    function getAppVersion() {
        return window.qPWASettings && window.qPWASettings.appVersion ? window.qPWASettings.appVersion : '';
    }

    // Track an event
    function trackEvent(eventName, data = {}) {
        // Don't track if analytics is disabled
        if (!qPWASettings.analyticsEnabled) {
            return;
        }

        // Add user and device info
        const eventData = {
            event_name: eventName,
            user_id: getUserId(),
            device_type: getDeviceType(),
            os: getOS(),
            browser: getBrowser(),
            app_version: getAppVersion(),
            timestamp: new Date().toISOString(),
            ...data
        };

        // For session events, add session ID
        if (eventName === 'session_start' || eventName === 'session_end') {
            eventData.session_id = localStorage.getItem('q_pwa_session_id');
        }

        // Send data to server
        fetch(qPWASettings.ajaxUrl, {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'q_pwa_track_event',
                nonce: qPWASettings.nonce,
                event_data: JSON.stringify(eventData)
            })
        }).catch(error => {
            // Store failed events for later retry
            const failedEvents = JSON.parse(localStorage.getItem('q_pwa_failed_events') || '[]');
            failedEvents.push(eventData);
            localStorage.setItem('q_pwa_failed_events', JSON.stringify(failedEvents));
        });
    }

    // Retry sending failed events
    function retryFailedEvents() {
        const failedEvents = JSON.parse(localStorage.getItem('q_pwa_failed_events') || '[]');
        if (failedEvents.length === 0) {
            return;
        }

        // Try to send each failed event
        const remainingEvents = [];
        failedEvents.forEach(eventData => {
            fetch(qPWASettings.ajaxUrl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'q_pwa_track_event',
                    nonce: qPWASettings.nonce,
                    event_data: JSON.stringify(eventData)
                })
            }).catch(() => {
                // Keep event in the failed list if it still fails
                remainingEvents.push(eventData);
            });
        });

        // Update failed events list
        localStorage.setItem('q_pwa_failed_events', JSON.stringify(remainingEvents));
    }

    // Track standard events
    function trackStandardEvents() {
        // Track page view
        trackEvent('pageview', {
            url: window.location.href,
            referrer: document.referrer
        });

        // Track app installation
        window.addEventListener('appinstalled', () => {
            trackEvent('install');
        });

        // Track app start
        if (window.matchMedia('(display-mode: standalone)').matches) {
            trackEvent('appstart');
        }

        // Track service worker registration
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(() => {
                trackEvent('sw_ready');
            });
        }
    }

    // Track session events
    function trackSessionEvents() {
        // Generate session ID
        function startSession() {
            const sessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('q_pwa_session_id', sessionId);
            localStorage.setItem('q_pwa_session_start', new Date().toISOString());
            
            trackEvent('session_start', {
                session_id: sessionId
            });
        }
        
        // End session when app is backgrounded or closed
        function endSession() {
            const sessionId = localStorage.getItem('q_pwa_session_id');
            const sessionStart = localStorage.getItem('q_pwa_session_start');
            
            if (sessionId && sessionStart) {
                const startTime = new Date(sessionStart);
                const endTime = new Date();
                const duration = endTime - startTime;
                
                trackEvent('session_end', {
                    session_id: sessionId,
                    session_duration: duration
                });
                
                localStorage.removeItem('q_pwa_session_id');
                localStorage.removeItem('q_pwa_session_start');
            }
        }
        
        // Start new session if none exists
        if (!localStorage.getItem('q_pwa_session_id')) {
            startSession();
        }
        
        // Track session end when page is hidden
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                endSession();
            }
        });
        
        // Also track when the window is closed
        window.addEventListener('beforeunload', () => {
            endSession();
        });
    }
    
    // Track device subscription
    function trackDeviceSubscription() {
        // Check if push is enabled
        if ('serviceWorker' in navigator && 'PushManager' in window) {
            navigator.serviceWorker.ready.then(registration => {
                registration.pushManager.getSubscription().then(subscription => {
                    if (subscription) {
                        trackEvent('push_subscribed', {
                            device_type: getDeviceType()
                        });
                    }
                });
            });
        }
    }
    
    // Track notification interactions
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then(registration => {
            if (registration && registration.active) {
                navigator.serviceWorker.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'notification_interaction') {
                        trackEvent('notification_' + event.data.action, event.data.payload || {});
                    }
                });
            }
        });
    }

    // Track install prompt events
    window.addEventListener('beforeinstallprompt', (e) => {
        trackEvent('install_prompt_shown');
        e.userChoice && e.userChoice.then(choice => {
            trackEvent('install_prompt_' + choice.outcome);
        });
    });
    window.addEventListener('appinstalled', () => {
        trackEvent('install_accepted');
    });

    // Track time on page
    let pageStartTime = Date.now();
    window.addEventListener('beforeunload', () => {
        const duration = Math.round((Date.now() - pageStartTime) / 1000);
        trackEvent('page_leave', { duration });
    });
    
    // Initialize tracking
    function init() {
        // Track standard events
        trackStandardEvents();
        
        // Track session events
        trackSessionEvents();
        
        // Track device subscription
        trackDeviceSubscription();
        
        // Retry failed events
        retryFailedEvents();
        
        // Expose tracking API
        window.qPWATrack = trackEvent;
    }

    // Initialize when document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();